﻿# Modern Front 2025 - Graphics Definitions

spriteTypes = {
	
	# Leader portraits
	spriteType = {
		name = "GFX_Portrait_Developer_Alex"
		texturefile = "gfx/leaders/DEV/Portrait_Developer_Alex.dds"
	}
	
	spriteType = {
		name = "GFX_Portrait_Developer_<PERSON>"
		texturefile = "gfx/leaders/DEV/Portrait_Developer_Sarah_<PERSON>.dds"
	}
	
	spriteType = {
		name = "GFX_Portrait_Developer_James_<PERSON>"
		texturefile = "gfx/leaders/DEV/Portrait_Developer_James_<PERSON>.dds"
	}
	
	# Country flags
	spriteType = {
		name = "GFX_flag_DEV_democratic"
		texturefile = "gfx/flags/DEV_democratic.tga"
	}
	
	spriteType = {
		name = "GFX_flag_DEV_neutrality"
		texturefile = "gfx/flags/DEV_neutrality.tga"
	}
	
	spriteType = {
		name = "GFX_flag_DEV_fascism"
		texturefile = "gfx/flags/DEV_fascism.tga"
	}
	
	spriteType = {
		name = "GFX_flag_DEV_communism"
		texturefile = "gfx/flags/DEV_communism.tga"
	}
	
	# Technology icons
	spriteType = {
		name = "GFX_modern_infantry_weapons_medium"
		texturefile = "gfx/interface/technologies/modern_infantry_weapons.dds"
	}
	
	spriteType = {
		name = "GFX_modern_armor_tech_medium"
		texturefile = "gfx/interface/technologies/modern_armor_tech.dds"
	}
	
	spriteType = {
		name = "GFX_cyber_warfare_tech_medium"
		texturefile = "gfx/interface/technologies/cyber_warfare_tech.dds"
	}
	
	spriteType = {
		name = "GFX_drone_technology_medium"
		texturefile = "gfx/interface/technologies/drone_technology.dds"
	}
	
	# Equipment icons
	spriteType = {
		name = "GFX_modern_rifle_equipment_medium"
		texturefile = "gfx/interface/equipments/modern_rifle_equipment.dds"
	}
	
	spriteType = {
		name = "GFX_modern_tank_equipment_medium"
		texturefile = "gfx/interface/equipments/modern_tank_equipment.dds"
	}
	
	spriteType = {
		name = "GFX_cyber_warfare_equipment_medium"
		texturefile = "gfx/interface/equipments/cyber_warfare_equipment.dds"
	}
	
	spriteType = {
		name = "GFX_military_drone_equipment_medium"
		texturefile = "gfx/interface/equipments/military_drone_equipment.dds"
	}
}
