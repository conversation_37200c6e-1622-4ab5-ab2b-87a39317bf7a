﻿# Modern Front 2025 - Motorized Equipment

equipments = {

	motorized_equipment = {
		year = 2020

		is_archetype = yes
		is_convertable = yes
		picture = archetype_motorized_equipment			
		is_buildable = no
		type = motorized
		group_by = archetype
		
		interface_category = interface_category_land
		
		#Offensive Abilities
		soft_attack = 4
		hard_attack = 0
		
		#Defensive Abilities
		defense = 5
		breakthrough = 18
		armor_value = 0

		#Misc Abilities
		maximum_speed = 12
		reliability = 0.8

		#Space taken in convoy
		lend_lease_cost = 1
		
		build_cost_ic = 2.5
		resources = {
			steel = 1
			rubber = 1
		}
		
		fuel_consumption = 1.2
	}

	motorized_equipment_1 = {
		year = 2020

		archetype = motorized_equipment
		parent = motorized_equipment
		priority = 10
		
		#Offensive Abilities
		soft_attack = 4
		hard_attack = 0
		
		#Defensive Abilities
		defense = 5
		breakthrough = 18

		#Misc Abilities
		maximum_speed = 12
		reliability = 0.8

		build_cost_ic = 2.5
		resources = {
			steel = 1
			rubber = 1
		}
		
		fuel_consumption = 1.2
	}

	motorized_equipment_2 = {
		year = 2025

		archetype = motorized_equipment
		parent = motorized_equipment_1
		priority = 20
		
		#Offensive Abilities
		soft_attack = 6
		hard_attack = 1
		
		#Defensive Abilities
		defense = 6
		breakthrough = 22

		#Misc Abilities
		maximum_speed = 14
		reliability = 0.85

		build_cost_ic = 3.0
		resources = {
			steel = 1
			rubber = 1
			aluminum = 1
		}
		
		fuel_consumption = 1.4
	}

	motorized_equipment_3 = {
		year = 2030

		archetype = motorized_equipment
		parent = motorized_equipment_2
		priority = 30
		
		#Offensive Abilities
		soft_attack = 8
		hard_attack = 2
		
		#Defensive Abilities
		defense = 8
		breakthrough = 26

		#Misc Abilities
		maximum_speed = 16
		reliability = 0.9

		build_cost_ic = 3.5
		resources = {
			steel = 1
			rubber = 1
			aluminum = 1
			tungsten = 1
		}
		
		fuel_consumption = 1.6
	}
}
