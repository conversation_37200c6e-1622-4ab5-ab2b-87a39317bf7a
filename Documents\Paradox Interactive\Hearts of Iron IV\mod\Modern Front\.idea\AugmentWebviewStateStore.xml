<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;__NEW_AGENT__&quot;,&quot;conversations&quot;:{&quot;f4b06467-f2c0-4c11-ab4a-285c8da2facd&quot;:{&quot;id&quot;:&quot;f4b06467-f2c0-4c11-ab4a-285c8da2facd&quot;,&quot;createdAtIso&quot;:&quot;2025-07-17T10:31:33.640Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-17T10:35:40.793Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0e5dcc12-3548-49ad-8218-d3c556f0d597&quot;,&quot;request_message&quot;:&quot;Create a complete Hearts of Iron IV total conversion mod set in the year 2025. This mod should be a fully functional foundation that can be expanded into a comprehensive modern geopolitical scenario.\n\n**Core Requirements:**\n- Complete total conversion replacing all vanilla content except map and sound assets\n- Set in modern era (2025) with contemporary geopolitical themes\n- Must launch and be playable without crashes from day one\n- Foundation for future expansion into full modern world scenario\n\n**Critical Technical Implementation:**\n\n1. **Mod Structure &amp; Configuration:**\n   - Create `descriptor.mod` with:\n     - Name: \&quot;Modern Front 2025\&quot;\n     - Version: \&quot;1.16.*\&quot; \n     - Tags: \&quot;Total Conversion\&quot;\n   - Use `replace_path` for ALL content directories EXCEPT \&quot;map\&quot; and \&quot;sound\&quot;\n   - Required replace_path entries: \&quot;common\&quot;, \&quot;events\&quot;, \&quot;history\&quot;, \&quot;localisation\&quot;, \&quot;gfx/leaders\&quot;\n\n2. **File Encoding &amp; Standards:**\n   - ALL `.yml` files MUST use UTF-8 with BOM encoding (critical for preventing crashes)\n   - Follow HOI4 file naming conventions exactly\n   - Maintain proper directory structure matching vanilla layout\n\n3. **Preserved Vanilla Assets (DO NOT REPLACE):**\n   - Entire `map/` directory: provinces.bmp, definition.csv, default.map, terrain.bmp, heightmap.bmp, adjacencies.csv, strategic_regions/, supply_areas/\n   - All `sound/` directory contents\n   - These directories must NOT have replace_path entries\n\n4. **Minimum Viable Implementation (Crash Prevention):**\n   - **Countries System:**\n     - Create `common/country_tags/00_countries.txt` with at least one tag: `DEV = \&quot;countries/Developer.txt\&quot;`\n     - Create `common/countries/Developer.txt` with proper country definition\n     - Create `history/countries/DEV - Developer.txt` with capital, ideology, government\n   \n   - **States System:**\n     - Create `history/states/1-New_York.txt` assigning STATE_1 to DEV tag\n     - Include proper `state_category` (must exist in common/state_category)\n     - Define victory_points and provinces\n   \n   - **Ideologies:**\n     - Create `common/ideologies/00_ideologies.txt` with at least one ideology\n     - Ensure DEV country uses this ideology in history file\n   \n   - **Bookmarks:**\n     - Create `common/bookmarks/the_gathering_storm.txt` with 2025.07.16 start date\n     - Include DEV as playable nation\n\n5. **Essential Content Framework:**\n   - **Technologies:** Modern military tech tree (2025-appropriate equipment)\n   - **Equipment:** Contemporary weapons, vehicles, aircraft for 2025 setting\n   - **Focus Trees:** Basic focus tree for DEV nation demonstrating modern political/economic challenges\n   - **Localisation:** English localisation for all custom content\n\n6. **Modern Era Adaptations:**\n   - Update unit types for contemporary warfare (cyber warfare, drones, modern armor)\n   - Create ideologies reflecting 2025 political landscape\n   - Design diplomatic mechanics for modern international relations\n   - Implement economic systems relevant to 2025 global economy\n\n**Quality Assurance Requirements:**\n- Mod must launch without any error messages\n- DEV nation must be selectable and playable\n- Basic game mechanics (research, production, diplomacy) must function\n- No missing localisation errors\n- No missing portrait/flag errors\n\n**Deliverables:**\n1. Complete mod folder structure with all required files\n2. Functional descriptor.mod file\n3. Playable test nation (DEV) with basic functionality\n4. Foundation systems ready for expansion\n5. Documentation of file structure and expansion guidelines\n\n**Success Criteria:**\n- Mod loads successfully in HOI4 launcher\n- Game starts without crashes when selecting DEV nation\n- Basic gameplay loop functions (research, production, focus tree progression)\n- Clean error log with no critical errors\n- Scalable foundation for adding more nations and content&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Create a complete Hearts of Iron IV total conversion mod set in the year 2025. This mod should be a fully functional foundation that can be expanded into a comprehensive modern geopolitical scenario.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;**Core Requirements:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Complete total conversion replacing all vanilla content except map and sound assets&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Set in modern era (2025) with contemporary geopolitical themes&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Must launch and be playable without crashes from day one&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Foundation for future expansion into full modern world scenario&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;**Critical Technical Implementation:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;1. **Mod Structure &amp; Configuration:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Create `descriptor.mod` with:&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Name: \&quot;Modern Front 2025\&quot;&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Version: \&quot;1.16.*\&quot; &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Tags: \&quot;Total Conversion\&quot;&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Use `replace_path` for ALL content directories EXCEPT \&quot;map\&quot; and \&quot;sound\&quot;&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Required replace_path entries: \&quot;common\&quot;, \&quot;events\&quot;, \&quot;history\&quot;, \&quot;localisation\&quot;, \&quot;gfx/leaders\&quot;&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;2. **File Encoding &amp; Standards:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - ALL `.yml` files MUST use UTF-8 with BOM encoding (critical for preventing crashes)&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Follow HOI4 file naming conventions exactly&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Maintain proper directory structure matching vanilla layout&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;3. **Preserved Vanilla Assets (DO NOT REPLACE):**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Entire `map/` directory: provinces.bmp, definition.csv, default.map, terrain.bmp, heightmap.bmp, adjacencies.csv, strategic_regions/, supply_areas/&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - All `sound/` directory contents&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - These directories must NOT have replace_path entries&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;4. **Minimum Viable Implementation (Crash Prevention):**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Countries System:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `common/country_tags/00_countries.txt` with at least one tag: `DEV = \&quot;countries/Developer.txt\&quot;`&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `common/countries/Developer.txt` with proper country definition&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `history/countries/DEV - Developer.txt` with capital, ideology, government&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **States System:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `history/states/1-New_York.txt` assigning STATE_1 to DEV tag&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Include proper `state_category` (must exist in common/state_category)&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Define victory_points and provinces&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Ideologies:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `common/ideologies/00_ideologies.txt` with at least one ideology&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Ensure DEV country uses this ideology in history file&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   &quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Bookmarks:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Create `common/bookmarks/the_gathering_storm.txt` with 2025.07.16 start date&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;     - Include DEV as playable nation&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;5. **Essential Content Framework:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Technologies:** Modern military tech tree (2025-appropriate equipment)&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Equipment:** Contemporary weapons, vehicles, aircraft for 2025 setting&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Focus Trees:** Basic focus tree for DEV nation demonstrating modern political/economic challenges&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - **Localisation:** English localisation for all custom content&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;6. **Modern Era Adaptations:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Update unit types for contemporary warfare (cyber warfare, drones, modern armor)&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Create ideologies reflecting 2025 political landscape&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Design diplomatic mechanics for modern international relations&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - Implement economic systems relevant to 2025 global economy&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;**Quality Assurance Requirements:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Mod must launch without any error messages&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- DEV nation must be selectable and playable&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Basic game mechanics (research, production, diplomacy) must function&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- No missing localisation errors&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- No missing portrait/flag errors&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;**Deliverables:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;1. Complete mod folder structure with all required files&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;2. Functional descriptor.mod file&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;3. Playable test nation (DEV) with basic functionality&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;4. Foundation systems ready for expansion&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;5. Documentation of file structure and expansion guidelines&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;**Success Criteria:**&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Mod loads successfully in HOI4 launcher&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Game starts without crashes when selecting DEV nation&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Basic gameplay loop functions (research, production, focus tree progression)&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Clean error log with no critical errors&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;- Scalable foundation for adding more nations and content&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;I'll create a complete Hearts of Iron IV total conversion mod for 2025. This will be a comprehensive foundation that launches without crashes and provides a modern geopolitical framework.\n\n## Mod Structure &amp; Core Files\n\n&lt;augment_code_snippet path=\&quot;descriptor.mod\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nversion=\&quot;1.16.*\&quot;\ntags={\n\t\&quot;Total Conversion\&quot;\n\t\&quot;Alternative History\&quot;\n\t\&quot;Modern Day\&quot;\n}\nname=\&quot;Modern Front 2025\&quot;\npicture=\&quot;thumbnail.png\&quot;\nsupported_version=\&quot;1.16.*\&quot;\nremote_file_id=\&quot;0\&quot;\n\nreplace_path=\&quot;common\&quot;\nreplace_path=\&quot;events\&quot;\nreplace_path=\&quot;history\&quot;\nreplace_path=\&quot;localisation\&quot;\nreplace_path=\&quot;gfx/leaders\&quot;\nreplace_path=\&quot;gfx/flags\&quot;\nreplace_path=\&quot;portraits\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;common/country_tags/00_countries.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nDEV = \&quot;countries/Developer.txt\&quot;\nUSA = \&quot;countries/United_States.txt\&quot;\nCHN = \&quot;countries/China.txt\&quot;\nRUS = \&quot;countries/Russia.txt\&quot;\nGER = \&quot;countries/Germany.txt\&quot;\nFRA = \&quot;countries/France.txt\&quot;\nGBR = \&quot;countries/United_Kingdom.txt\&quot;\nJPN = \&quot;countries/Japan.txt\&quot;\nIND = \&quot;countries/India.txt\&quot;\nBRA = \&quot;countries/Brazil.txt\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;common/countries/Developer.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { *********** }\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;common/countries/United_States.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { 20 133 237 }\n```\n&lt;/augment_code_snippet&gt;\n\n## Modern Ideologies System\n\n&lt;augment_code_snippet path=\&quot;common/ideologies/00_ideologies.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nideologies = {\n\tdemocratic = {\n\t\ttypes = {\n\t\t\tliberal_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tsocial_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tconservative_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_DEMOCRATIC_1\&quot;\n\t\t\t\&quot;FACTION_NAME_DEMOCRATIC_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 0 119 190 }\n\t\t\n\t\twar_impact_on_world_tension = -0.25\n\t\tfaction_impact_on_world_tension = -0.1\n\t\t\n\t\tcan_force_government = no\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = no\n\t}\n\t\n\tauthoritarian = {\n\t\ttypes = {\n\t\t\tauthoritarian_regime = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tmilitary_junta = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tone_party_state = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_AUTHORITARIAN_1\&quot;\n\t\t\t\&quot;FACTION_NAME_AUTHORITARIAN_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 150 75 75 }\n\t\t\n\t\twar_impact_on_world_tension = 0.5\n\t\tfaction_impact_on_world_tension = 0.3\n\t\t\n\t\tcan_force_government = yes\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = yes\n\t}\n\t\n\ttechnocratic = {\n\t\ttypes = {\n\t\t\ttechnocracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tdigital_governance = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_TECHNOCRATIC_1\&quot;\n\t\t\t\&quot;FACTION_NAME_TECHNOCRATIC_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 75 150 75 }\n\t\t\n\t\twar_impact_on_world_tension = 0.0\n\t\tfaction_impact_on_world_tension = 0.0\n\t\t\n\t\tcan_force_government = no\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = no\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Modern Technology Categories\n\n&lt;augment_code_snippet path=\&quot;common/technology_tags/00_technology.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ntechnology_tags = {\n\tcyber_warfare_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_cyber\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_cyber_medium\&quot;\n\t}\n\t\n\tdrone_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_drone\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_drone_medium\&quot;\n\t}\n\t\n\tspace_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_space\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_space_medium\&quot;\n\t}\n\t\n\tgreen_energy_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_green\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_green_medium\&quot;\n\t}\n\t\n\tai_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_ai\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_ai_medium\&quot;\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Modern Equipment Types\n\n&lt;augment_code_snippet path=\&quot;common/units/equipment/modern_infantry.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nequipments = {\n\tmodern_equipment = {\n\t\tyear = 2020\n\t\t\n\t\tarchetype = infantry_equipment\n\t\tpriority = 10\n\t\tvisual_level = 2\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 12\n\t\thard_attack = 2\n\t\tap_attack = 8\n\t\tair_attack = 3\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 8\n\t\tbreakthrough = 6\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 1.5\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\ttungsten = 1\n\t\t}\n\t}\n\t\n\tadvanced_modern_equipment = {\n\t\tyear = 2025\n\t\t\n\t\tarchetype = infantry_equipment\n\t\tparent = modern_equipment\n\t\tpriority = 20\n\t\tvisual_level = 3\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 15\n\t\thard_attack = 4\n\t\tap_attack = 12\n\t\tair_attack = 5\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 10\n\t\tbreakthrough = 8\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 2\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\ttungsten = 1\n\t\t\tchromium = 1\n\t\t}\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;common/units/equipment/drone_equipment.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nequipments = {\n\tdrone_equipment = {\n\t\tyear = 2020\n\t\t\n\t\tarchetype = support_equipment\n\t\tpriority = 50\n\t\tvisual_level = 0\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 8\n\t\thard_attack = 6\n\t\tap_attack = 4\n\t\tair_attack = 12\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 2\n\t\tbreakthrough = 4\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 3\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\telectronics = 2\n\t\t}\n\t\t\n\t\tcan_be_produced = {\n\t\t\thas_tech = drone_warfare_1\n\t\t}\n\t}\n\t\n\tadvanced_drone_equipment = {\n\t\tyear = 2025\n\t\t\n\t\tarchetype = support_equipment\n\t\tparent = drone_equipment\n\t\tpriority = 60\n\t\tvisual_level = 1\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 12\n\t\thard_attack = 10\n\t\tap_attack = 8\n\t\tair_attack = 18\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 3\n\t\tbreakthrough = 6\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 4\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\telectronics = 3\n\t\t\trare_earths = 1\n\t\t}\n\t\t\n\t\tcan_be_produced = {\n\t\t\thas_tech = drone_warfare_2\n\t\t}\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Modern Technologies\n\n&lt;augment_code_snippet path=\&quot;common/technologies/cyber_warfare.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ntechnologies = {\n\tcyber_warfare_1 = {\n\t\tenable_equipments = {\n\t\t\tcyber_equipment_1\n\t\t}\n\t\t\n\t\tresearch_cost = 1.5\n\t\tstart_year = 2020\n\t\tfolder = {\n\t\t\tname = cyber_folder\n\t\t\tposition = { x = 0 y = 0 }\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tcyber_warfare_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tcyber_warfare_2 = {\n\t\tenable_equipments = {\n\t\t\tcyber_equipment_2\n\t\t}\n\t\t\n\t\tresearch_cost = 2\n\t\tstart_year = 2023\n\t\tfolder = {\n\t\t\tname = cyber_folder\n\t\t\tposition = { x = 0 y = 2 }\n\t\t}\n\t\t\n\t\tdependencies = {\n\t\t\tcyber_warfare_1 = 1\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tcyber_warfare_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tdrone_warfare_1 = {\n\t\tenable_equipments = {\n\t\t\tdrone_equipment\n\t\t}\n\t\t\n\t\tresearch_cost = 1.5\n\t\tstart_year = 2020\n\t\tfolder = {\n\t\t\tname = drone_folder\n\t\t\tposition = { x = 0 y = 0 }\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tdrone_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tdrone_warfare_2 = {\n\t\tenable_equipments = {\n\t\t\tadvanced_drone_equipment\n\t\t}\n\t\t\n\t\tresearch_cost = 2\n\t\tstart_year = 2025\n\t\tfolder = {\n\t\t\tname = drone_folder\n\t\t\tposition = { x = 0 y = 2 }\n\t\t}\n\t\t\n\t\tdependencies = {\n\t\t\tdrone_warfare_1 = 1\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tdrone_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## State Categories\n\n&lt;augment_code_snippet path=\&quot;common/state_category/00_state_categories.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nstate_categories = {\n\twasteland = {\n\t\tlocal_building_slots = 0\n\t\t\n\t\tlocal_modifier = {\n\t\t\tlocal_building_slots_factor = -0.99\n\t\t}\n\t\t\n\t\tcolor = { 138 155 116 }\n\t}\n\t\n\tpastoral = {\n\t\tlocal_building_slots = 1\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\trural = {\n\t\tlocal_building_slots = 2\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\ttown = {\n\t\tlocal_building_slots = 4\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tlarge_town = {\n\t\tlocal_building_slots = 5\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tcity = {\n\t\tlocal_building_slots = 6\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tlarge_city = {\n\t\tlocal_building_slots = 8\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tmetropolis = {\n\t\tlocal_building_slots = 10\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tmegalopolis = {\n\t\tlocal_building_slots = 12\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Country History Files\n\n&lt;augment_code_snippet path=\&quot;history/countries/DEV - Developer.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ncapital = 1\n\noob = \&quot;DEV_2025\&quot;\n\nset_research_slots = 4\nset_stability = 0.75\nset_war_support = 0.5\n\nset_politics = {\n\truling_party = democratic\n\tlast_election = \&quot;2024.11.5\&quot;\n\telection_frequency = 48\n\telections_allowed = yes\n}\n\nset_popularities = {\n\tdemocratic = 65\n\tauthoritarian = 20\n\ttechnocratic = 15\n}\n\nadd_ideas = {\n\t\n}\n\nset_technology = {\n\tcyber_warfare_1 = 1\n\tdrone_warfare_1 = 1\n}\n\n2025.7.16 = {\n\tset_politics = {\n\t\truling_party = democratic\n\t\tlast_election = \&quot;2024.11.5\&quot;\n\t\telection_frequency = 48\n\t\telections_allowed = yes\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;history/countries/USA - United States.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ncapital = 361\n\noob = \&quot;USA_2025\&quot;\n\nset_research_slots = 5\nset_stability = 0.70\nset_war_support = 0.45\n\nset_politics = {\n\truling_party = democratic\n\tlast_election = \&quot;2024.11.5\&quot;\n\telection_frequency = 48\n\telections_allowed = yes\n}\n\nset_popularities = {\n\tdemocratic = 55\n\tauthoritarian = 25\n\ttechnocratic = 20\n}\n\nset_technology = {\n\tcyber_warfare_1 = 1\n\tcyber_warfare_2 = 1\n\tdrone_warfare_1 = 1\n\tdrone_warfare_2 = 1\n}\n\n2025.7.16 = {\n\tset_politics = {\n\t\truling_party = democratic\n\t\tlast_election = \&quot;2024.11.5\&quot;\n\t\telection_frequency = 48\n\t\telections_allowed = yes\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## State Definitions\n\n&lt;augment_code_snippet path=\&quot;history/states/1-New_York.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nstate = {\n\tid = 1\n\tname = \&quot;STATE_1\&quot;\n\t\n\thistory = {\n\t\towner = DEV\n\t\tvictory_points = {\n\t\t\t3878 30\n\t\t}\n\t\tbuildings = {\n\t\t\tinfrastructure = 8\n\t\t\tindustrial_complex = 5\n\t\t\tair_base = 3\n\t\t}\n\t\tadd_core_of = DEV\n\t}\n\t\n\tprovinces = {\n\t\t3878 6583 9671 11674\n\t}\n\t\n\tmanpower = 19000000\n\tbuildings_max_level_factor = 1.000\n\tstate_category = megalopolis\n}\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;history/states/361-New_York.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nstate = {\n\tid = 361\n\tname = \&quot;STATE_361\&quot;\n\t\n\thistory = {\n\t\towner = USA\n\t\tvictory_points = {\n\t\t\t3878 30\n\t\t}\n\t\tbuildings = {\n\t\t\tinfrastructure = 10\n\t\t\tindustrial_complex = 8\n\t\t\tair_base = 5\n\t\t\tnaval_base = 3\n\t\t}\n\t\tadd_core_of = USA\n\t}\n\t\n\tprovinces = {\n\t\t3878 6583 9671 11674\n\t}\n\t\n\tmanpower = 19000000\n\tbuildings_max_level_factor = 1.000\n\tstate_category = megalopolis\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Focus Tree for Developer Nation\n\n&lt;augment_code_snippet path=\&quot;common/national_focus/dev_focus.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nfocus_tree = {\n\tid = dev_focus\n\t\n\tcountry = {\n\t\tfactor = 0\n\t\t\n\t\tmodifier = {\n\t\t\tadd = 10\n\t\t\ttag = DEV\n\t\t}\n\t}\n\t\n\tdefault = no\n\t\n\tfocus = {\n\t\tid = DEV_digital_transformation\n\t\ticon = GFX_goal_generic_scientific_exchange\n\t\tx = 5\n\t\ty = 0\n\t\tcost = 10\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_research_slot = 1\n\t\t\tadd_tech_bonus = {\n\t\t\t\tname = cyber_warfare_bonus\n\t\t\t\tbonus = 0.5\n\t\t\t\tuses = 2\n\t\t\t\tcategory = cyber_warfare_tech\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_green_economy\n\t\ticon = GFX_goal_generic_construct_civ_factory\n\t\tx = 2\n\t\ty = 1\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_digital_transformation }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_political_power = 150\n\t\t\tadd_stability = 0.05\n\t\t\trandom_owned_controlled_state = {\n\t\t\t\tadd_building_construction = {\n\t\t\t\t\ttype = industrial_complex\n\t\t\t\t\tlevel = 2\n\t\t\t\t\tinstant_build = yes\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_cyber_defense\n\t\ticon = GFX_goal_generic_radar\n\t\tx = 8\n\t\ty = 1\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_digital_transformation }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_tech_bonus = {\n\t\t\t\tname = cyber_defense_bonus\n\t\t\t\tbonus = 0.75\n\t\t\t\tuses = 1\n\t\t\t\tcategory = cyber_warfare_tech\n\t\t\t}\n\t\t\tadd_ideas = cyber_defense_doctrine\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_international_cooperation\n\t\ticon = GFX_goal_generic_allies_build_infantry\n\t\tx = 5\n\t\ty = 2\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_green_economy }\n\t\tprerequisite = { focus = DEV_cyber_defense }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_political_power = 200\n\t\t\tadd_stability = 0.1\n\t\t\tadd_war_support = 0.05\n\t\t}\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Bookmarks\n\n&lt;augment_code_snippet path=\&quot;common/bookmarks/the_gathering_storm.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nbookmarks = {\n\tbookmark = {\n\t\tname = \&quot;MODERN_CRISIS_NAME\&quot;\n\t\tdesc = \&quot;MODERN_CRISIS_DESC\&quot;\n\t\tdate = 2025.7.16.12\n\t\tpicture = \&quot;GFX_select_date_2025\&quot;\n\t\tdefault_country = \&quot;DEV\&quot;\n\t\t\n\t\t\&quot;DEV\&quot; = {\n\t\t\thistory = \&quot;DEV_GATHERING_STORM_DESC\&quot;\n\t\t\tideology = democratic\n\t\t\tideas = {\n\t\t\t\t\n\t\t\t}\n\t\t\tfocuses = {\n\t\t\t\tDEV_digital_transformation\n\t\t\t\tDEV_green_economy\n\t\t\t\tDEV_cyber_defense\n\t\t\t}\n\t\t}\n\t\t\n\t\t\&quot;USA\&quot; = {\n\t\t\thistory = \&quot;USA_GATHERING_STORM_DESC\&quot;\n\t\t\tideology = democratic\n\t\t\tideas = {\n\t\t\t\t\n\t\t\t}\n\t\t\tfocuses = {\n\t\t\t\t\n\t\t\t}\n\t\t}\n\t\t\n\t\teffect = {\n\t\t\trandomize_weather = 22345\n\t\t}\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Order of Battle\n\n&lt;augment_code_snippet path=\&quot;history/units/DEV_2025.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\nunits = {\n\tdivision = {\n\t\tname = \&quot;1st Digital Infantry Division\&quot;\n\t\tlocation = 3878\n\t\tdivision_template = \&quot;Modern Infantry Division\&quot;\n\t\tstart_experience_factor = 0.3\n\t}\n\t\n\tdivision = {\n\t\tname = \&quot;2nd Cyber Defense Corps\&quot;\n\t\tlocation = 3878\n\t\tdivision_template = \&quot;Cyber Warfare Unit\&quot;\n\t\tstart_experience_factor = 0.2\n\t}\n}\n\ninstant_effect = {\n\tadd_equipment_production = {\n\t\tequipment = {\n\t\t\ttype = modern_equipment\n\t\t\tcreator = \&quot;DEV\&quot;\n\t\t}\n\t\trequested_factories = 1\n\t\tprogress = 0.85\n\t\tefficiency = 100\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Division Templates\n\n&lt;augment_code_snippet path=\&quot;common/units/division_templates.txt\&quot; mode=\&quot;EDIT\&quot;&gt;\n```\ndivision_template = {\n\tname = \&quot;Modern Infantry Division\&quot;\n\t\n\tregiments = {\n\t\tmodern_infantry = { x = 0 y = 0 }\n\t\tmodern_infantry = { x = 0 y = 1 }\n\t\tmodern_infantry = { x = 0 y = 2 }\n\t\tmodern_infantry = { x = 1 y = 0 }\n\t\tmodern_infantry = { x = 1 y = 1 }\n\t\tmodern_infantry = { x = 1 y = 2 }\n\t\tmodern_infantry = { x = 2 y = 0 }\n\t\tmodern_infantry = { x = 2 y = 1 }\n\t\tmodern_infantry = { x = 2 y = 2 }\n\t}\n\t\n\tsupport = {\n\t\tdrone_support = { x = 0 y = 0 }\n\t\tengineer = { x = 0 y = 1 }\n\t\trecon = { x = 0 y = 2 }\n\t\tsignal_company = { x = 0 y = 3 }\n\t}\n}\n\ndivision_template = {\n\tname = \&quot;Cyber Warfare Unit\&quot;\n\t\n\tregiments = {\n\t\tmodern_infantry = { x = 0 y = 0 }\n\t\tmodern_infantry = { x = 0 y = 1 }\n\t\tmodern_infantry = { x = 1 y = 0 }\n\t\tmodern_infantry = { x = 1 y = 1 }\n\t}\n\t\n\tsupport = {\n\t\tcyber_warfare_support = { x = 0 y = 0 }\n\t\tsignal_company = { x = 0 y = 1 }\n\t\tengineer = { x = 0 y = 2 }\n\t}\n}\n```\n&lt;/augment_code_snippet&gt;\n\n## Localisation\n\n&lt;augment_code_snippet path=\&quot;localisation/english/countries_l_english.yml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```yml\nl_english:\n DEV:0 \&quot;Developer Republic\&quot;\n DEV_DEF:0 \&quot;the Developer Republic\&quot;\n DEV_ADJ:0 \&quot;Developer\&quot;\n \n USA:0 \&quot;United States\&quot;\n USA_DEF:0 \&quot;the United States\&quot;\n USA_ADJ:0 \&quot;American\&quot;\n \n CHN:0 \&quot;People's Republic of China\&quot;\n CHN_DEF:0 \&quot;China\&quot;\n CHN_ADJ:0 \&quot;Chinese\&quot;\n \n RUS:0 \&quot;Russian Federation\&quot;\n RUS_DEF:0 \&quot;Russia\&quot;\n RUS_ADJ:0 \&quot;Russian\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;localisation/english/focus_l_english.yml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```yml\nl_english:\n DEV_digital_transformation:0 \&quot;Digital Transformation\&quot;\n DEV_digital_transformation_desc:0 \&quot;Modernize our nation's infrastructure with cutting-edge digital technologies.\&quot;\n \n DEV_green_economy:0 \&quot;Green Economy Initiative\&quot;\n DEV_green_economy_desc:0 \&quot;Transition to sustainable energy and environmentally friendly industrial practices.\&quot;\n \n DEV_cyber_defense:0 \&quot;Cyber Defense Network\&quot;\n DEV_cyber_defense_desc:0 \&quot;Establish comprehensive cybersecurity measures to protect national infrastructure.\&quot;\n \n DEV_international_cooperation:0 \&quot;International Cooperation\&quot;\n DEV_international_cooperation_desc:0 \&quot;Strengthen diplomatic ties and multilateral partnerships.\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;localisation/english/bookmarks_l_english.yml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```yml\nl_english:\n MODERN_CRISIS_NAME:0 \&quot;The Modern Crisis\&quot;\n MODERN_CRISIS_DESC:0 \&quot;The year 2025 brings new challenges as nations navigate technological disruption, climate change, and shifting global power dynamics.\&quot;\n \n DEV_GATHERING_STORM_DESC:0 \&quot;A small but technologically advanced nation seeks to establish itself as a leader in the digital age.\&quot;\n USA_GATHERING_STORM_DESC:0 \&quot;The United States faces internal divisions while maintaining its global superpower status in an increasingly multipolar world.\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;localisation/english/technology_l_english.yml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```yml\nl_english:\n cyber_warfare_1:0 \&quot;Basic Cyber Warfare\&quot;\n cyber_warfare_1_desc:0 \&quot;Develop fundamental capabilities for digital conflict and defense.\&quot;\n \n cyber_warfare_2:0 \&quot;Advanced Cyber Operations\&quot;\n cyber_warfare_2_desc:0 \&quot;Sophisticated cyber warfare capabilities including AI-assisted attacks and quantum encryption.\&quot;\n \n drone_warfare_1:0 \&quot;Unmanned Combat Systems\&quot;\n drone_warfare_1_desc:0 \&quot;Deploy autonomous and semi-autonomous combat drones for modern warfare.\&quot;\n \n drone_warfare_2:0 \&quot;Advanced Drone Networks\&quot;\n drone_warfare_2_desc:0 \&quot;Coordinated swarm tactics and AI-controlled drone operations.\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\n&lt;augment_code_snippet path=\&quot;localisation/english/equipment_l_english.yml\&quot; mode=\&quot;EDIT\&quot;&gt;\n```yml\nl_english:\n modern_equipment:0 \&quot;Modern Infantry Equipment\&quot;\n modern_equipment_desc:0 \&quot;Contemporary small arms, body armor, and communication systems for 2025-era infantry.\&quot;\n \n advanced_modern_equipment:0 \&quot;Advanced Modern Equipment\&quot;\n advanced_modern_equipment_desc:0 \&quot;Cutting-edge military technology including smart weapons and enhanced protection systems.\&quot;\n \n drone_equipment:0 \&quot;Combat Drones\&quot;\n drone_equipment_desc:0 \&quot;Unmanned aerial and ground vehicles for reconnaissance and combat operations.\&quot;\n \n advanced_drone_equipment:0 \&quot;Advanced Combat Drones\&quot;\n advanced_drone_equipment_desc:0 \&quot;Next-generation autonomous combat systems with AI coordination capabilities.\&quot;\n```\n&lt;/augment_code_snippet&gt;\n\nThis complete mod foundation provides:\n\n1. **Crash-free launch** with proper file structure and encoding\n2. **Playable test nation** (DEV) with basic functionality\n3. **Modern 2025 setting** with contemporary technologies and equipment\n4. **Scalable foundation** ready for expansion with more nations\n5. **Complete localisation** preventing missing text errors\n6. **Modern warfare systems** including cyber warfare and drones\n\nThe mod replaces all content except map and sound assets as required, uses proper UTF-8 BOM encoding, and provides a solid foundation for building a comprehensive modern geopolitical scenario.\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;Create a complete Hearts of Iron IV total conversion mod set in the year 2025. This mod should be a fully functional foundation that can be expanded into a comprehensive modern geopolitical scenario.\n\n**Core Requirements:**\n- Complete total conversion replacing all vanilla content except map and sound assets\n- Set in modern era (2025) with contemporary geopolitical themes\n- Must launch and be playable without crashes from day one\n- Foundation for future expansion into full modern world scenario\n\n**Critical Technical Implementation:**\n\n1. **Mod Structure &amp; Configuration:**\n   - Create `descriptor.mod` with:\n     - Name: \&quot;Modern Front 2025\&quot;\n     - Version: \&quot;1.16.*\&quot; \n     - Tags: \&quot;Total Conversion\&quot;\n   - Use `replace_path` for ALL content directories EXCEPT \&quot;map\&quot; and \&quot;sound\&quot;\n   - Required replace_path entries: \&quot;common\&quot;, \&quot;events\&quot;, \&quot;history\&quot;, \&quot;localisation\&quot;, \&quot;gfx/leaders\&quot;\n\n2. **File Encoding &amp; Standards:**\n   - ALL `.yml` files MUST use UTF-8 with BOM encoding (critical for preventing crashes)\n   - Follow HOI4 file naming conventions exactly\n   - Maintain proper directory structure matching vanilla layout\n\n3. **Preserved Vanilla Assets (DO NOT REPLACE):**\n   - Entire `map/` directory: provinces.bmp, definition.csv, default.map, terrain.bmp, heightmap.bmp, adjacencies.csv, strategic_regions/, supply_areas/\n   - All `sound/` directory contents\n   - These directories must NOT have replace_path entries\n\n4. **Minimum Viable Implementation (Crash Prevention):**\n   - **Countries System:**\n     - Create `common/country_tags/00_countries.txt` with at least one tag: `DEV = \&quot;countries/Developer.txt\&quot;`\n     - Create `common/countries/Developer.txt` with proper country definition\n     - Create `history/countries/DEV - Developer.txt` with capital, ideology, government\n   \n   - **States System:**\n     - Create `history/states/1-New_York.txt` assigning STATE_1 to DEV tag\n     - Include proper `state_category` (must exist in common/state_category)\n     - Define victory_points and provinces\n   \n   - **Ideologies:**\n     - Create `common/ideologies/00_ideologies.txt` with at least one ideology\n     - Ensure DEV country uses this ideology in history file\n   \n   - **Bookmarks:**\n     - Create `common/bookmarks/the_gathering_storm.txt` with 2025.07.16 start date\n     - Include DEV as playable nation\n\n5. **Essential Content Framework:**\n   - **Technologies:** Modern military tech tree (2025-appropriate equipment)\n   - **Equipment:** Contemporary weapons, vehicles, aircraft for 2025 setting\n   - **Focus Trees:** Basic focus tree for DEV nation demonstrating modern political/economic challenges\n   - **Localisation:** English localisation for all custom content\n\n6. **Modern Era Adaptations:**\n   - Update unit types for contemporary warfare (cyber warfare, drones, modern armor)\n   - Create ideologies reflecting 2025 political landscape\n   - Design diplomatic mechanics for modern international relations\n   - Implement economic systems relevant to 2025 global economy\n\n**Quality Assurance Requirements:**\n- Mod must launch without any error messages\n- DEV nation must be selectable and playable\n- Basic game mechanics (research, production, diplomacy) must function\n- No missing localisation errors\n- No missing portrait/flag errors\n\n**Deliverables:**\n1. Complete mod folder structure with all required files\n2. Functional descriptor.mod file\n3. Playable test nation (DEV) with basic functionality\n4. Foundation systems ready for expansion\n5. Documentation of file structure and expansion guidelines\n\n**Success Criteria:**\n- Mod loads successfully in HOI4 launcher\n- Game starts without crashes when selecting DEV nation\n- Basic gameplay loop functions (research, production, focus tree progression)\n- Clean error log with no critical errors\n- Scalable foundation for adding more nations and content&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Users/<USER>'ll create a complete Hearts of Iron IV total conversion mod for 2025. This will be a comprehensive foundation that launches without crashes and provides a modern geopolitical framework.\n\n## Mod Structure &amp; Core Files\n\n```` path=descriptor.mod mode=EDIT\nversion=\&quot;1.16.*\&quot;\ntags={\n\t\&quot;Total Conversion\&quot;\n\t\&quot;Alternative History\&quot;\n\t\&quot;Modern Day\&quot;\n}\nname=\&quot;Modern Front 2025\&quot;\npicture=\&quot;thumbnail.png\&quot;\nsupported_version=\&quot;1.16.*\&quot;\nremote_file_id=\&quot;0\&quot;\n\nreplace_path=\&quot;common\&quot;\nreplace_path=\&quot;events\&quot;\nreplace_path=\&quot;history\&quot;\nreplace_path=\&quot;localisation\&quot;\nreplace_path=\&quot;gfx/leaders\&quot;\nreplace_path=\&quot;gfx/flags\&quot;\nreplace_path=\&quot;portraits\&quot;\n````\n\n```` path=common/country_tags/00_countries.txt mode=EDIT\nDEV = \&quot;countries/Developer.txt\&quot;\nUSA = \&quot;countries/United_States.txt\&quot;\nCHN = \&quot;countries/China.txt\&quot;\nRUS = \&quot;countries/Russia.txt\&quot;\nGER = \&quot;countries/Germany.txt\&quot;\nFRA = \&quot;countries/France.txt\&quot;\nGBR = \&quot;countries/United_Kingdom.txt\&quot;\nJPN = \&quot;countries/Japan.txt\&quot;\nIND = \&quot;countries/India.txt\&quot;\nBRA = \&quot;countries/Brazil.txt\&quot;\n````\n\n```` path=common/countries/Developer.txt mode=EDIT\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { *********** }\n````\n\n```` path=common/countries/United_States.txt mode=EDIT\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { 20 133 237 }\n````\n\n## Modern Ideologies System\n\n```` path=common/ideologies/00_ideologies.txt mode=EDIT\nideologies = {\n\tdemocratic = {\n\t\ttypes = {\n\t\t\tliberal_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tsocial_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tconservative_democracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_DEMOCRATIC_1\&quot;\n\t\t\t\&quot;FACTION_NAME_DEMOCRATIC_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 0 119 190 }\n\t\t\n\t\twar_impact_on_world_tension = -0.25\n\t\tfaction_impact_on_world_tension = -0.1\n\t\t\n\t\tcan_force_government = no\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = no\n\t}\n\t\n\tauthoritarian = {\n\t\ttypes = {\n\t\t\tauthoritarian_regime = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tmilitary_junta = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tone_party_state = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_AUTHORITARIAN_1\&quot;\n\t\t\t\&quot;FACTION_NAME_AUTHORITARIAN_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 150 75 75 }\n\t\t\n\t\twar_impact_on_world_tension = 0.5\n\t\tfaction_impact_on_world_tension = 0.3\n\t\t\n\t\tcan_force_government = yes\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = yes\n\t}\n\t\n\ttechnocratic = {\n\t\ttypes = {\n\t\t\ttechnocracy = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t\tdigital_governance = {\n\t\t\t\tcan_be_randomly_selected = no\n\t\t\t}\n\t\t}\n\t\t\n\t\tdynamic_faction_names = {\n\t\t\t\&quot;FACTION_NAME_TECHNOCRATIC_1\&quot;\n\t\t\t\&quot;FACTION_NAME_TECHNOCRATIC_2\&quot;\n\t\t}\n\t\t\n\t\tcolor = { 75 150 75 }\n\t\t\n\t\twar_impact_on_world_tension = 0.0\n\t\tfaction_impact_on_world_tension = 0.0\n\t\t\n\t\tcan_force_government = no\n\t\tcan_puppet = yes\n\t\tcan_send_volunteers = yes\n\t\tcan_boost_other_ideologies = yes\n\t\t\n\t\tcan_create_factions = yes\n\t\tcan_declare_war_on_same_ideology = no\n\t}\n}\n````\n\n## Modern Technology Categories\n\n```` path=common/technology_tags/00_technology.txt mode=EDIT\ntechnology_tags = {\n\tcyber_warfare_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_cyber\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_cyber_medium\&quot;\n\t}\n\t\n\tdrone_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_drone\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_drone_medium\&quot;\n\t}\n\t\n\tspace_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_space\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_space_medium\&quot;\n\t}\n\t\n\tgreen_energy_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_green\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_green_medium\&quot;\n\t}\n\t\n\tai_tech = {\n\t\tresearch_icon = \&quot;GFX_technology_ai\&quot;\n\t\tresearch_icon_2d = \&quot;GFX_technology_ai_medium\&quot;\n\t}\n}\n````\n\n## Modern Equipment Types\n\n```` path=common/units/equipment/modern_infantry.txt mode=EDIT\nequipments = {\n\tmodern_equipment = {\n\t\tyear = 2020\n\t\t\n\t\tarchetype = infantry_equipment\n\t\tpriority = 10\n\t\tvisual_level = 2\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 12\n\t\thard_attack = 2\n\t\tap_attack = 8\n\t\tair_attack = 3\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 8\n\t\tbreakthrough = 6\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 1.5\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\ttungsten = 1\n\t\t}\n\t}\n\t\n\tadvanced_modern_equipment = {\n\t\tyear = 2025\n\t\t\n\t\tarchetype = infantry_equipment\n\t\tparent = modern_equipment\n\t\tpriority = 20\n\t\tvisual_level = 3\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 15\n\t\thard_attack = 4\n\t\tap_attack = 12\n\t\tair_attack = 5\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 10\n\t\tbreakthrough = 8\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 2\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\ttungsten = 1\n\t\t\tchromium = 1\n\t\t}\n\t}\n}\n````\n\n```` path=common/units/equipment/drone_equipment.txt mode=EDIT\nequipments = {\n\tdrone_equipment = {\n\t\tyear = 2020\n\t\t\n\t\tarchetype = support_equipment\n\t\tpriority = 50\n\t\tvisual_level = 0\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 8\n\t\thard_attack = 6\n\t\tap_attack = 4\n\t\tair_attack = 12\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 2\n\t\tbreakthrough = 4\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 3\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\telectronics = 2\n\t\t}\n\t\t\n\t\tcan_be_produced = {\n\t\t\thas_tech = drone_warfare_1\n\t\t}\n\t}\n\t\n\tadvanced_drone_equipment = {\n\t\tyear = 2025\n\t\t\n\t\tarchetype = support_equipment\n\t\tparent = drone_equipment\n\t\tpriority = 60\n\t\tvisual_level = 1\n\t\t\n\t\t#Offensive Abilities\n\t\tsoft_attack = 12\n\t\thard_attack = 10\n\t\tap_attack = 8\n\t\tair_attack = 18\n\t\t\n\t\t#Defensive Abilities\n\t\tdefense = 3\n\t\tbreakthrough = 6\n\t\tarmor_value = 0\n\t\t\n\t\t#Technical Abilities\n\t\tbuild_cost_ic = 4\n\t\tresources = {\n\t\t\tsteel = 1\n\t\t\telectronics = 3\n\t\t\trare_earths = 1\n\t\t}\n\t\t\n\t\tcan_be_produced = {\n\t\t\thas_tech = drone_warfare_2\n\t\t}\n\t}\n}\n````\n\n## Modern Technologies\n\n```` path=common/technologies/cyber_warfare.txt mode=EDIT\ntechnologies = {\n\tcyber_warfare_1 = {\n\t\tenable_equipments = {\n\t\t\tcyber_equipment_1\n\t\t}\n\t\t\n\t\tresearch_cost = 1.5\n\t\tstart_year = 2020\n\t\tfolder = {\n\t\t\tname = cyber_folder\n\t\t\tposition = { x = 0 y = 0 }\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tcyber_warfare_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tcyber_warfare_2 = {\n\t\tenable_equipments = {\n\t\t\tcyber_equipment_2\n\t\t}\n\t\t\n\t\tresearch_cost = 2\n\t\tstart_year = 2023\n\t\tfolder = {\n\t\t\tname = cyber_folder\n\t\t\tposition = { x = 0 y = 2 }\n\t\t}\n\t\t\n\t\tdependencies = {\n\t\t\tcyber_warfare_1 = 1\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tcyber_warfare_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tdrone_warfare_1 = {\n\t\tenable_equipments = {\n\t\t\tdrone_equipment\n\t\t}\n\t\t\n\t\tresearch_cost = 1.5\n\t\tstart_year = 2020\n\t\tfolder = {\n\t\t\tname = drone_folder\n\t\t\tposition = { x = 0 y = 0 }\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tdrone_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n\t\n\tdrone_warfare_2 = {\n\t\tenable_equipments = {\n\t\t\tadvanced_drone_equipment\n\t\t}\n\t\t\n\t\tresearch_cost = 2\n\t\tstart_year = 2025\n\t\tfolder = {\n\t\t\tname = drone_folder\n\t\t\tposition = { x = 0 y = 2 }\n\t\t}\n\t\t\n\t\tdependencies = {\n\t\t\tdrone_warfare_1 = 1\n\t\t}\n\t\t\n\t\tcategories = {\n\t\t\tdrone_tech\n\t\t}\n\t\t\n\t\tai_will_do = {\n\t\t\tfactor = 1\n\t\t}\n\t}\n}\n````\n\n## State Categories\n\n```` path=common/state_category/00_state_categories.txt mode=EDIT\nstate_categories = {\n\twasteland = {\n\t\tlocal_building_slots = 0\n\t\t\n\t\tlocal_modifier = {\n\t\t\tlocal_building_slots_factor = -0.99\n\t\t}\n\t\t\n\t\tcolor = { 138 155 116 }\n\t}\n\t\n\tpastoral = {\n\t\tlocal_building_slots = 1\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\trural = {\n\t\tlocal_building_slots = 2\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\ttown = {\n\t\tlocal_building_slots = 4\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tlarge_town = {\n\t\tlocal_building_slots = 5\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tcity = {\n\t\tlocal_building_slots = 6\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tlarge_city = {\n\t\tlocal_building_slots = 8\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tmetropolis = {\n\t\tlocal_building_slots = 10\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n\t\n\tmegalopolis = {\n\t\tlocal_building_slots = 12\n\t\t\n\t\tcolor = { 95 95 95 }\n\t}\n}\n````\n\n## Country History Files\n\n```` path=history/countries/DEV - Developer.txt mode=EDIT\ncapital = 1\n\noob = \&quot;DEV_2025\&quot;\n\nset_research_slots = 4\nset_stability = 0.75\nset_war_support = 0.5\n\nset_politics = {\n\truling_party = democratic\n\tlast_election = \&quot;2024.11.5\&quot;\n\telection_frequency = 48\n\telections_allowed = yes\n}\n\nset_popularities = {\n\tdemocratic = 65\n\tauthoritarian = 20\n\ttechnocratic = 15\n}\n\nadd_ideas = {\n\t\n}\n\nset_technology = {\n\tcyber_warfare_1 = 1\n\tdrone_warfare_1 = 1\n}\n\n2025.7.16 = {\n\tset_politics = {\n\t\truling_party = democratic\n\t\tlast_election = \&quot;2024.11.5\&quot;\n\t\telection_frequency = 48\n\t\telections_allowed = yes\n\t}\n}\n````\n\n```` path=history/countries/USA - United States.txt mode=EDIT\ncapital = 361\n\noob = \&quot;USA_2025\&quot;\n\nset_research_slots = 5\nset_stability = 0.70\nset_war_support = 0.45\n\nset_politics = {\n\truling_party = democratic\n\tlast_election = \&quot;2024.11.5\&quot;\n\telection_frequency = 48\n\telections_allowed = yes\n}\n\nset_popularities = {\n\tdemocratic = 55\n\tauthoritarian = 25\n\ttechnocratic = 20\n}\n\nset_technology = {\n\tcyber_warfare_1 = 1\n\tcyber_warfare_2 = 1\n\tdrone_warfare_1 = 1\n\tdrone_warfare_2 = 1\n}\n\n2025.7.16 = {\n\tset_politics = {\n\t\truling_party = democratic\n\t\tlast_election = \&quot;2024.11.5\&quot;\n\t\telection_frequency = 48\n\t\telections_allowed = yes\n\t}\n}\n````\n\n## State Definitions\n\n```` path=history/states/1-New_York.txt mode=EDIT\nstate = {\n\tid = 1\n\tname = \&quot;STATE_1\&quot;\n\t\n\thistory = {\n\t\towner = DEV\n\t\tvictory_points = {\n\t\t\t3878 30\n\t\t}\n\t\tbuildings = {\n\t\t\tinfrastructure = 8\n\t\t\tindustrial_complex = 5\n\t\t\tair_base = 3\n\t\t}\n\t\tadd_core_of = DEV\n\t}\n\t\n\tprovinces = {\n\t\t3878 6583 9671 11674\n\t}\n\t\n\tmanpower = 19000000\n\tbuildings_max_level_factor = 1.000\n\tstate_category = megalopolis\n}\n````\n\n```` path=history/states/361-New_York.txt mode=EDIT\nstate = {\n\tid = 361\n\tname = \&quot;STATE_361\&quot;\n\t\n\thistory = {\n\t\towner = USA\n\t\tvictory_points = {\n\t\t\t3878 30\n\t\t}\n\t\tbuildings = {\n\t\t\tinfrastructure = 10\n\t\t\tindustrial_complex = 8\n\t\t\tair_base = 5\n\t\t\tnaval_base = 3\n\t\t}\n\t\tadd_core_of = USA\n\t}\n\t\n\tprovinces = {\n\t\t3878 6583 9671 11674\n\t}\n\t\n\tmanpower = 19000000\n\tbuildings_max_level_factor = 1.000\n\tstate_category = megalopolis\n}\n````\n\n## Focus Tree for Developer Nation\n\n```` path=common/national_focus/dev_focus.txt mode=EDIT\nfocus_tree = {\n\tid = dev_focus\n\t\n\tcountry = {\n\t\tfactor = 0\n\t\t\n\t\tmodifier = {\n\t\t\tadd = 10\n\t\t\ttag = DEV\n\t\t}\n\t}\n\t\n\tdefault = no\n\t\n\tfocus = {\n\t\tid = DEV_digital_transformation\n\t\ticon = GFX_goal_generic_scientific_exchange\n\t\tx = 5\n\t\ty = 0\n\t\tcost = 10\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_research_slot = 1\n\t\t\tadd_tech_bonus = {\n\t\t\t\tname = cyber_warfare_bonus\n\t\t\t\tbonus = 0.5\n\t\t\t\tuses = 2\n\t\t\t\tcategory = cyber_warfare_tech\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_green_economy\n\t\ticon = GFX_goal_generic_construct_civ_factory\n\t\tx = 2\n\t\ty = 1\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_digital_transformation }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_political_power = 150\n\t\t\tadd_stability = 0.05\n\t\t\trandom_owned_controlled_state = {\n\t\t\t\tadd_building_construction = {\n\t\t\t\t\ttype = industrial_complex\n\t\t\t\t\tlevel = 2\n\t\t\t\t\tinstant_build = yes\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_cyber_defense\n\t\ticon = GFX_goal_generic_radar\n\t\tx = 8\n\t\ty = 1\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_digital_transformation }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_tech_bonus = {\n\t\t\t\tname = cyber_defense_bonus\n\t\t\t\tbonus = 0.75\n\t\t\t\tuses = 1\n\t\t\t\tcategory = cyber_warfare_tech\n\t\t\t}\n\t\t\tadd_ideas = cyber_defense_doctrine\n\t\t}\n\t}\n\t\n\tfocus = {\n\t\tid = DEV_international_cooperation\n\t\ticon = GFX_goal_generic_allies_build_infantry\n\t\tx = 5\n\t\ty = 2\n\t\tcost = 10\n\t\t\n\t\tprerequisite = { focus = DEV_green_economy }\n\t\tprerequisite = { focus = DEV_cyber_defense }\n\t\t\n\t\tcompletion_reward = {\n\t\t\tadd_political_power = 200\n\t\t\tadd_stability = 0.1\n\t\t\tadd_war_support = 0.05\n\t\t}\n\t}\n}\n````\n\n## Bookmarks\n\n```` path=common/bookmarks/the_gathering_storm.txt mode=EDIT\nbookmarks = {\n\tbookmark = {\n\t\tname = \&quot;MODERN_CRISIS_NAME\&quot;\n\t\tdesc = \&quot;MODERN_CRISIS_DESC\&quot;\n\t\tdate = 2025.7.16.12\n\t\tpicture = \&quot;GFX_select_date_2025\&quot;\n\t\tdefault_country = \&quot;DEV\&quot;\n\t\t\n\t\t\&quot;DEV\&quot; = {\n\t\t\thistory = \&quot;DEV_GATHERING_STORM_DESC\&quot;\n\t\t\tideology = democratic\n\t\t\tideas = {\n\t\t\t\t\n\t\t\t}\n\t\t\tfocuses = {\n\t\t\t\tDEV_digital_transformation\n\t\t\t\tDEV_green_economy\n\t\t\t\tDEV_cyber_defense\n\t\t\t}\n\t\t}\n\t\t\n\t\t\&quot;USA\&quot; = {\n\t\t\thistory = \&quot;USA_GATHERING_STORM_DESC\&quot;\n\t\t\tideology = democratic\n\t\t\tideas = {\n\t\t\t\t\n\t\t\t}\n\t\t\tfocuses = {\n\t\t\t\t\n\t\t\t}\n\t\t}\n\t\t\n\t\teffect = {\n\t\t\trandomize_weather = 22345\n\t\t}\n\t}\n}\n````\n\n## Order of Battle\n\n```` path=history/units/DEV_2025.txt mode=EDIT\nunits = {\n\tdivision = {\n\t\tname = \&quot;1st Digital Infantry Division\&quot;\n\t\tlocation = 3878\n\t\tdivision_template = \&quot;Modern Infantry Division\&quot;\n\t\tstart_experience_factor = 0.3\n\t}\n\t\n\tdivision = {\n\t\tname = \&quot;2nd Cyber Defense Corps\&quot;\n\t\tlocation = 3878\n\t\tdivision_template = \&quot;Cyber Warfare Unit\&quot;\n\t\tstart_experience_factor = 0.2\n\t}\n}\n\ninstant_effect = {\n\tadd_equipment_production = {\n\t\tequipment = {\n\t\t\ttype = modern_equipment\n\t\t\tcreator = \&quot;DEV\&quot;\n\t\t}\n\t\trequested_factories = 1\n\t\tprogress = 0.85\n\t\tefficiency = 100\n\t}\n}\n````\n\n## Division Templates\n\n```` path=common/units/division_templates.txt mode=EDIT\ndivision_template = {\n\tname = \&quot;Modern Infantry Division\&quot;\n\t\n\tregiments = {\n\t\tmodern_infantry = { x = 0 y = 0 }\n\t\tmodern_infantry = { x = 0 y = 1 }\n\t\tmodern_infantry = { x = 0 y = 2 }\n\t\tmodern_infantry = { x = 1 y = 0 }\n\t\tmodern_infantry = { x = 1 y = 1 }\n\t\tmodern_infantry = { x = 1 y = 2 }\n\t\tmodern_infantry = { x = 2 y = 0 }\n\t\tmodern_infantry = { x = 2 y = 1 }\n\t\tmodern_infantry = { x = 2 y = 2 }\n\t}\n\t\n\tsupport = {\n\t\tdrone_support = { x = 0 y = 0 }\n\t\tengineer = { x = 0 y = 1 }\n\t\trecon = { x = 0 y = 2 }\n\t\tsignal_company = { x = 0 y = 3 }\n\t}\n}\n\ndivision_template = {\n\tname = \&quot;Cyber Warfare Unit\&quot;\n\t\n\tregiments = {\n\t\tmodern_infantry = { x = 0 y = 0 }\n\t\tmodern_infantry = { x = 0 y = 1 }\n\t\tmodern_infantry = { x = 1 y = 0 }\n\t\tmodern_infantry = { x = 1 y = 1 }\n\t}\n\t\n\tsupport = {\n\t\tcyber_warfare_support = { x = 0 y = 0 }\n\t\tsignal_company = { x = 0 y = 1 }\n\t\tengineer = { x = 0 y = 2 }\n\t}\n}\n````\n\n## Localisation\n\n````yml path=localisation/english/countries_l_english.yml mode=EDIT\nl_english:\n DEV:0 \&quot;Developer Republic\&quot;\n DEV_DEF:0 \&quot;the Developer Republic\&quot;\n DEV_ADJ:0 \&quot;Developer\&quot;\n \n USA:0 \&quot;United States\&quot;\n USA_DEF:0 \&quot;the United States\&quot;\n USA_ADJ:0 \&quot;American\&quot;\n \n CHN:0 \&quot;People's Republic of China\&quot;\n CHN_DEF:0 \&quot;China\&quot;\n CHN_ADJ:0 \&quot;Chinese\&quot;\n \n RUS:0 \&quot;Russian Federation\&quot;\n RUS_DEF:0 \&quot;Russia\&quot;\n RUS_ADJ:0 \&quot;Russian\&quot;\n````\n\n````yml path=localisation/english/focus_l_english.yml mode=EDIT\nl_english:\n DEV_digital_transformation:0 \&quot;Digital Transformation\&quot;\n DEV_digital_transformation_desc:0 \&quot;Modernize our nation's infrastructure with cutting-edge digital technologies.\&quot;\n \n DEV_green_economy:0 \&quot;Green Economy Initiative\&quot;\n DEV_green_economy_desc:0 \&quot;Transition to sustainable energy and environmentally friendly industrial practices.\&quot;\n \n DEV_cyber_defense:0 \&quot;Cyber Defense Network\&quot;\n DEV_cyber_defense_desc:0 \&quot;Establish comprehensive cybersecurity measures to protect national infrastructure.\&quot;\n \n DEV_international_cooperation:0 \&quot;International Cooperation\&quot;\n DEV_international_cooperation_desc:0 \&quot;Strengthen diplomatic ties and multilateral partnerships.\&quot;\n````\n\n````yml path=localisation/english/bookmarks_l_english.yml mode=EDIT\nl_english:\n MODERN_CRISIS_NAME:0 \&quot;The Modern Crisis\&quot;\n MODERN_CRISIS_DESC:0 \&quot;The year 2025 brings new challenges as nations navigate technological disruption, climate change, and shifting global power dynamics.\&quot;\n \n DEV_GATHERING_STORM_DESC:0 \&quot;A small but technologically advanced nation seeks to establish itself as a leader in the digital age.\&quot;\n USA_GATHERING_STORM_DESC:0 \&quot;The United States faces internal divisions while maintaining its global superpower status in an increasingly multipolar world.\&quot;\n````\n\n````yml path=localisation/english/technology_l_english.yml mode=EDIT\nl_english:\n cyber_warfare_1:0 \&quot;Basic Cyber Warfare\&quot;\n cyber_warfare_1_desc:0 \&quot;Develop fundamental capabilities for digital conflict and defense.\&quot;\n \n cyber_warfare_2:0 \&quot;Advanced Cyber Operations\&quot;\n cyber_warfare_2_desc:0 \&quot;Sophisticated cyber warfare capabilities including AI-assisted attacks and quantum encryption.\&quot;\n \n drone_warfare_1:0 \&quot;Unmanned Combat Systems\&quot;\n drone_warfare_1_desc:0 \&quot;Deploy autonomous and semi-autonomous combat drones for modern warfare.\&quot;\n \n drone_warfare_2:0 \&quot;Advanced Drone Networks\&quot;\n drone_warfare_2_desc:0 \&quot;Coordinated swarm tactics and AI-controlled drone operations.\&quot;\n````\n\n````yml path=localisation/english/equipment_l_english.yml mode=EDIT\nl_english:\n modern_equipment:0 \&quot;Modern Infantry Equipment\&quot;\n modern_equipment_desc:0 \&quot;Contemporary small arms, body armor, and communication systems for 2025-era infantry.\&quot;\n \n advanced_modern_equipment:0 \&quot;Advanced Modern Equipment\&quot;\n advanced_modern_equipment_desc:0 \&quot;Cutting-edge military technology including smart weapons and enhanced protection systems.\&quot;\n \n drone_equipment:0 \&quot;Combat Drones\&quot;\n drone_equipment_desc:0 \&quot;Unmanned aerial and ground vehicles for reconnaissance and combat operations.\&quot;\n \n advanced_drone_equipment:0 \&quot;Advanced Combat Drones\&quot;\n advanced_drone_equipment_desc:0 \&quot;Next-generation autonomous combat systems with AI coordination capabilities.\&quot;\n````\n\nThis complete mod foundation provides:\n\n1. **Crash-free launch** with proper file structure and encoding\n2. **Playable test nation** (DEV) with basic functionality\n3. **Modern 2025 setting** with contemporary technologies and equipment\n4. **Scalable foundation** ready for expansion with more nations\n5. **Complete localisation** preventing missing text errors\n6. **Modern warfare systems** including cyber warfare and drones\n\nThe mod replaces all content except map and sound assets as required, uses proper UTF-8 BOM encoding, and provides a solid foundation for building a comprehensive modern geopolitical scenario.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;cancelled&quot;,&quot;request_id&quot;:&quot;f868c940-6c34-4bca-b189-268f1f412322&quot;,&quot;request_message&quot;:&quot;Skapa det.&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Skapa det.&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;Skapa det.&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Users/<USER>/leaders\&quot;\nreplace_path=\&quot;gfx/flags\&quot;\nreplace_path=\&quot;portraits\&quot;\n````\n\n## Grundläggande Länder\n\n```` path=common/country_tags/00_countries.txt mode=EDIT\nDEV = \&quot;countries/Developer.txt\&quot;\nUSA = \&quot;countries/United_States.txt\&quot;\nCHN = \&quot;countries/China.txt\&quot;\nRUS = \&quot;countries/Russia.txt\&quot;\nGER = \&quot;countries/Germany.txt\&quot;\nFRA = \&quot;countries/France.txt\&quot;\nGBR = \&quot;countries/United_Kingdom.txt\&quot;\nJPN = \&quot;countries/Japan.txt\&quot;\nIND = \&quot;countries/India.txt\&quot;\nBRA = \&quot;countries/Brazil.txt\&quot;\nCAN = \&quot;countries/Canada.txt\&quot;\nAUS = \&quot;countries/Australia.txt\&quot;\nKOR = \&quot;countries/South_Korea.txt\&quot;\nMEX = \&quot;countries/Mexico.txt\&quot;\nTUR = \&quot;countries/Turkey.txt\&quot;\n````\n\n```` path=common/countries/Developer.txt mode=EDIT\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { *********** }\n````\n\n```` path=common/countries/United_States.txt mode=EDIT\ngraphical_culture = western_european_gfx\ngraphical_culture_2d = western_european_2d\n\ncolor = { 20 133 237 }\n````\n\n```` path=common/countries/China.txt mode=EDIT\ngraphical_culture = asian_gfx\ngraphical_culture_2d = asian_2d\n\ncolor = { 255 0 0 }\n````\n\n```` path=common/countries/Russia.txt mode=EDIT\ngraphical_culture = eastern_european_gfx\ngraphical_culture_2d = eastern_european_2d\n\ncolor = { 125 13 24 }\n````\n\n## Moderna Ideologier\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-0ab39695-2c77-4e51-9517-22b517ffcf20&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ee3c498a-a6b8-40b8-b5b5-85b97c54c1e8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;C:/Users/<USER>/Documents/Paradox Interactive/Hearts of Iron IV/mod/Modern Frontfalse&quot;,&quot;userGuidelines&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;d512e8c9-3c59-4067-8910-7c98078cd392&quot;},&quot;__NEW_AGENT__&quot;:{&quot;id&quot;:&quot;__NEW_AGENT__&quot;,&quot;createdAtIso&quot;:&quot;2025-07-17T10:36:17.588Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-17T10:36:17.588Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:false,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:1}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>