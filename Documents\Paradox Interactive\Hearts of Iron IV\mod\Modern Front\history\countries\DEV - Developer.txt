# Developer Test Nation - Modern Front 2025

capital = 1

# Starting tech
set_technology = {
	# 2025 Basic Technologies - only use technologies that exist
	modern_infantry_weapons = 1
	modern_armor_tech = 1
	cyber_warfare_tech = 1
	drone_technology = 1
}

# Starting politics
set_politics = {
	ruling_party = democratic
	last_election = "2024.11.05"
	election_frequency = 48
	elections_allowed = yes
}

set_popularities = {
	democratic = 65
	neutrality = 20
	fascism = 5
	communism = 10
}

# Country leader
create_country_leader = {
	name = "Alex Developer"
	desc = "POLITICS_ALEX_DEVELOPER_DESC"
	picture = "Portrait_Developer_Alex.dds"
	expire = "2029.1.1"
	ideology = liberal_democracy
	traits = {
		# Modern leader traits would go here
	}
}

# Military leaders
create_field_marshal = {
	name = "General <PERSON>"
	desc = "GENERAL_SARAH_MITCHELL_DESC"
	picture = "Portrait_Developer_Sarah_Mitchell.dds"
	traits = { }
	skill = 4
	attack_skill = 3
	defense_skill = 4
	planning_skill = 4
	logistics_skill = 3
}

create_corps_commander = {
	name = "Colonel <PERSON>"
	desc = "COLONEL_JAMES_RODRIGUEZ_DESC"
	picture = "Portrait_Developer_James_Rodriguez.dds"
	traits = { }
	skill = 3
	attack_skill = 3
	defense_skill = 3
	planning_skill = 3
	logistics_skill = 2
}

# Starting equipment
set_convoys = 50

# Starting resources
add_ideas = {
	# Modern nation ideas would go here
}

# Starting manpower
set_stability = 0.7
set_war_support = 0.3

# Starting factories
2025.7.16 = {
	add_political_power = 150
	
	# Starting buildings would be set in state files
	
	# Starting units
	create_navy = {
		name = "Developer Navy"
		base = 1
		location = 1
		ship = { name = "DNS Prototype" definition = destroyer equipment = { destroyer_1 = { amount = 1 owner = DEV } } }
	}
}
